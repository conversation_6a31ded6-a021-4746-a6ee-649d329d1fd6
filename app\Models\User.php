<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\JadwalMasal;
use App\Models\Absensi;
use App\Traits\HasMentions;
use Filament\Panel;
use Filament\Models\Contracts\FilamentUser;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable, HasMentions, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'karyawan_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the employee profile associated with this user
     */

    // filament isAdmin
    public function canAccessPanel(Panel $panel): bool
    {
        // Allow access to karyawan panel for user that has karyawan_id
        if ($panel->getId() === 'karyawan') {
            return $this->karyawan()->exists();
        }

        // Allow access to admin panel for users with admin role or Shield roles
        if ($panel->getId() === 'admin') {
            // exist anyrole karyawan
            // if ($this->hasAnyRole(['karyawan'])) {
            //     return false;
            // }
            return true;
        }

        return true;
    }

    public function karyawan()
    {
        return $this->hasOne(Karyawan::class, 'id_user', 'id');
    }

    /**
     * Get the employees supervised by this user
     */
    public function supervisedEmployees()
    {
        return $this->hasMany(Karyawan::class, 'supervisor_id');
    }

    /**
     * Get the schedules created by this user
     */
    public function createdSchedules()
    {
        return $this->hasMany(Schedule::class, 'supervisor_id');
    }

    /**
     * Get the bulk schedules created by this user
     */
    public function createdBulkSchedules()
    {
        return $this->hasMany(JadwalMasal::class, 'created_by');
    }

    /**
     * Get the attendance records approved by this user
     */
    public function approvedAttendance()
    {
        return $this->hasMany(Absensi::class, 'approved_by');
    }

    /**
     * Check if the user is a supervisor
     */
    public function isSupervisor()
    {
        return $this->role === 'supervisor' || $this->role === 'admin';
    }

    /**
     * Check if the user is an admin
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    // assignedTasks
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }
    public function createdTasks()
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    public function getFullNameAttribute()
    {
        return $this->karyawan->nama ?? '';
    }

    /**
     * Check if user has any role (from either system)
     *
     * @param string|array $roles Role name(s) to check
     * @return bool
     */
    public function hasAnySystemRole($roles)
    {
        $roles = is_array($roles) ? $roles : [$roles];

        // Check built-in role field
        if (in_array($this->role, $roles)) {
            return true;
        }

        // Check Spatie roles
        return $this->hasAnyRole($roles);
    }

    public function getDepartmentNameAttribute()
    {
        return $this->karyawan->departemen->nama_departemen ?? '';
    }
}
