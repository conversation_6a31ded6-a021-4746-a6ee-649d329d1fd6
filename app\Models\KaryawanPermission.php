<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KaryawanPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'karyawan_id',
        'permission_type',
        'scope_type',
        'scope_values',
        'is_active',
        'description',
        'created_by',
    ];

    protected $casts = [
        'scope_values' => 'array',
        'is_active' => 'boolean',
    ];

    // Permission types constants
    const PERMISSION_TYPES = [
        'approve_cuti' => 'Approve Cuti/Izin/Sakit',
        'view_absensi' => 'Lihat Absensi',
        'manage_jadwal' => 'Kelola Jadwal',
        'view_payroll' => 'Lihat Payroll',
        'manage_karyawan' => 'Kelola Data Karyawan',
        'view_reports' => 'Lihat Laporan',
        'manage_overtime' => 'Kelola <PERSON>',
        'approve_overtime' => 'Approve Lembur',
    ];

    // Scope types constants
    const SCOPE_TYPES = [
        'all' => 'Semua Data',
        'entitas' => 'Berdasarkan Entitas',
        'departemen' => 'Berdasarkan Departemen',
        'divisi' => 'Berdasarkan Divisi',
        'custom' => 'Custom (Pilih Karyawan)',
    ];

    /**
     * Relationship dengan karyawan
     */
    public function karyawan(): BelongsTo
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Relationship dengan user yang membuat permission
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get permission type label
     */
    public function getPermissionTypeLabelAttribute(): string
    {
        return self::PERMISSION_TYPES[$this->permission_type] ?? $this->permission_type;
    }

    /**
     * Get scope type label
     */
    public function getScopeTypeLabelAttribute(): string
    {
        return self::SCOPE_TYPES[$this->scope_type] ?? $this->scope_type;
    }

    /**
     * Get scope description
     */
    public function getScopeDescriptionAttribute(): string
    {
        switch ($this->scope_type) {
            case 'all':
                return 'Semua data dalam sistem';
            
            case 'entitas':
                if (empty($this->scope_values)) return 'Tidak ada entitas dipilih';
                $entitas = Entitas::whereIn('id', $this->scope_values)->pluck('nama')->toArray();
                return 'Entitas: ' . implode(', ', $entitas);
            
            case 'departemen':
                if (empty($this->scope_values)) return 'Tidak ada departemen dipilih';
                $departemen = Departemen::whereIn('id', $this->scope_values)->pluck('nama_departemen')->toArray();
                return 'Departemen: ' . implode(', ', $departemen);
            
            case 'divisi':
                if (empty($this->scope_values)) return 'Tidak ada divisi dipilih';
                $divisi = Divisi::whereIn('id', $this->scope_values)->pluck('nama_divisi')->toArray();
                return 'Divisi: ' . implode(', ', $divisi);
            
            case 'custom':
                if (empty($this->scope_values)) return 'Tidak ada karyawan dipilih';
                $karyawan = Karyawan::whereIn('id', $this->scope_values)->pluck('nama_lengkap')->toArray();
                $count = count($karyawan);
                if ($count <= 3) {
                    return 'Karyawan: ' . implode(', ', $karyawan);
                } else {
                    return 'Karyawan: ' . implode(', ', array_slice($karyawan, 0, 3)) . " dan {$count} lainnya";
                }
            
            default:
                return 'Scope tidak dikenal';
        }
    }

    /**
     * Check if user has permission for specific data
     */
    public static function hasPermission(int $karyawanId, string $permissionType, $targetData = null): bool
    {
        $permissions = self::where('karyawan_id', $karyawanId)
            ->where('permission_type', $permissionType)
            ->where('is_active', true)
            ->get();

        if ($permissions->isEmpty()) {
            return false;
        }

        foreach ($permissions as $permission) {
            if (self::checkScopePermission($permission, $targetData)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check scope permission
     */
    private static function checkScopePermission(KaryawanPermission $permission, $targetData): bool
    {
        switch ($permission->scope_type) {
            case 'all':
                return true;
            
            case 'entitas':
                if (!$targetData || !isset($targetData->id_entitas)) return false;
                return in_array($targetData->id_entitas, $permission->scope_values ?? []);
            
            case 'departemen':
                if (!$targetData || !isset($targetData->id_departemen)) return false;
                return in_array($targetData->id_departemen, $permission->scope_values ?? []);
            
            case 'divisi':
                if (!$targetData || !isset($targetData->id_divisi)) return false;
                return in_array($targetData->id_divisi, $permission->scope_values ?? []);
            
            case 'custom':
                if (!$targetData || !isset($targetData->id)) return false;
                return in_array($targetData->id, $permission->scope_values ?? []);
            
            default:
                return false;
        }
    }

    /**
     * Get accessible data IDs for a permission type
     */
    public static function getAccessibleDataIds(int $karyawanId, string $permissionType, string $dataType = 'karyawan'): array
    {
        $permissions = self::where('karyawan_id', $karyawanId)
            ->where('permission_type', $permissionType)
            ->where('is_active', true)
            ->get();

        if ($permissions->isEmpty()) {
            return [];
        }

        $accessibleIds = [];

        foreach ($permissions as $permission) {
            switch ($permission->scope_type) {
                case 'all':
                    // Return all active karyawan IDs
                    return Karyawan::where('status_aktif', true)->pluck('id')->toArray();
                
                case 'entitas':
                    $ids = Karyawan::whereIn('id_entitas', $permission->scope_values ?? [])
                        ->where('status_aktif', true)
                        ->pluck('id')
                        ->toArray();
                    $accessibleIds = array_merge($accessibleIds, $ids);
                    break;
                
                case 'departemen':
                    $ids = Karyawan::whereIn('id_departemen', $permission->scope_values ?? [])
                        ->where('status_aktif', true)
                        ->pluck('id')
                        ->toArray();
                    $accessibleIds = array_merge($accessibleIds, $ids);
                    break;
                
                case 'divisi':
                    $ids = Karyawan::whereIn('id_divisi', $permission->scope_values ?? [])
                        ->where('status_aktif', true)
                        ->pluck('id')
                        ->toArray();
                    $accessibleIds = array_merge($accessibleIds, $ids);
                    break;
                
                case 'custom':
                    $accessibleIds = array_merge($accessibleIds, $permission->scope_values ?? []);
                    break;
            }
        }

        return array_unique($accessibleIds);
    }
}
