<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Absensi extends Model
{
    use HasFactory;

    protected $table = 'absensi';

    protected $fillable = [
        'karyawan_id',
        'jadwal_id',
        'periode',
        'tanggal_absensi',
        'waktu_masuk',
        'waktu_keluar',
        'status',
        'keterangan',
        'lokasi_masuk',
        'lokasi_keluar',
        'latitude_masuk',
        'longitude_masuk',
        'latitude_keluar',
        'longitude_keluar',
        'foto_masuk',
        'foto_keluar',
        'metadata_foto_masuk',
        'metadata_foto_keluar',
        'approved_by',
        'approved_at',
        // Compatibility fields for mass assignment
        'latitude',
        'longitude',
        'foto',
        'foto_absensi',
    ];

    protected $casts = [
        'tanggal_absensi' => 'date',
        'waktu_masuk' => 'datetime',
        'waktu_keluar' => 'datetime',
        'approved_at' => 'datetime',
        'latitude_masuk' => 'decimal:17',
        'longitude_masuk' => 'decimal:17',
        'latitude_keluar' => 'decimal:17',
        'longitude_keluar' => 'decimal:17',
        'metadata_foto_masuk' => 'array',
        'metadata_foto_keluar' => 'array',
    ];

    /**
     * Get the employee associated with this attendance record
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Get the schedule associated with this attendance record
     */
    public function jadwal()
    {
        return $this->belongsTo(Schedule::class, 'jadwal_id');
    }

    /**
     * Get the supervisor who approved this attendance record
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if the attendance is late based on the schedule and tolerance
     */
    public function getIsLateAttribute()
    {
        if (!$this->jadwal || !$this->jadwal->shift || !$this->waktu_masuk) {
            return false;
        }

        $shift = $this->jadwal->shift;
        $actualEntry = \Carbon\Carbon::parse($this->waktu_masuk);

        // Handle split shift
        if ($shift->isSplitShift()) {
            $currentPeriod = $shift->getCurrentPeriod($actualEntry->format('H:i:s'));
            $periods = $shift->getWorkPeriods();

            foreach ($periods as $period) {
                if ($period['periode'] == $currentPeriod) {
                    $shiftStart = \Carbon\Carbon::parse($period['waktu_mulai']);
                    $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;

                    return $actualEntry->greaterThan($shiftStart->addMinutes($toleranceMinutes));
                }
            }
        } else {
            // Regular shift logic
            $shiftStart = \Carbon\Carbon::parse($shift->waktu_mulai);
            $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;

            return $actualEntry->greaterThan($shiftStart->addMinutes($toleranceMinutes));
        }

        return false;
    }

    /**
     * Get lateness minutes using AttendanceService
     */
    public function getLatenessMinutesAttribute()
    {
        return \App\Services\AttendanceService::calculateLatenessMinutes($this);
    }

    /**
     * Get entitas name from schedule or karyawan
     */
    public function getEntitasNameAttribute()
    {
        // First try to get entitas from schedule (more specific)
        if ($this->jadwal && $this->jadwal->entitas) {
            return $this->jadwal->entitas->nama;
        }

        // Fallback to karyawan's entitas
        if ($this->karyawan && $this->karyawan->entitas) {
            return $this->karyawan->entitas->nama;
        }

        return null;
    }

    /**
     * Get entitas ID from schedule or karyawan
     */
    public function getEntitasIdAttribute()
    {
        // First try to get entitas from schedule (more specific)
        if ($this->jadwal && $this->jadwal->entitas_id) {
            return $this->jadwal->entitas_id;
        }

        // Fallback to karyawan's entitas
        if ($this->karyawan && $this->karyawan->id_entitas) {
            return $this->karyawan->id_entitas;
        }

        return null;
    }

    /**
     * Get entitas model from schedule or karyawan
     */
    public function getEntitasAttribute()
    {
        // First try to get entitas from schedule (more specific)
        if ($this->jadwal && $this->jadwal->entitas) {
            return $this->jadwal->entitas;
        }

        // Fallback to karyawan's entitas
        if ($this->karyawan && $this->karyawan->entitas) {
            return $this->karyawan->entitas;
        }

        return null;
    }

    /**
     * Check if the attendance is approved
     */
    public function getIsApprovedAttribute()
    {
        return !is_null($this->approved_at);
    }

    /**
     * Calculate work duration in minutes
     */
    public function getDurasiKerjaAttribute()
    {
        if (!$this->waktu_masuk || !$this->waktu_keluar) {
            return 0;
        }

        $masuk = \Carbon\Carbon::parse($this->waktu_masuk);
        $keluar = \Carbon\Carbon::parse($this->waktu_keluar);

        // Handle cross-midnight shifts
        if ($keluar->lessThan($masuk)) {
            $keluar->addDay();
        }

        return $keluar->diffInMinutes($masuk);
    }

    /**
     * Mutator for compatibility - set latitude (maps to latitude_masuk)
     */
    public function setLatitudeAttribute($value)
    {
        $this->attributes['latitude_masuk'] = $value;
    }

    /**
     * Mutator for compatibility - set longitude (maps to longitude_masuk)
     */
    public function setLongitudeAttribute($value)
    {
        $this->attributes['longitude_masuk'] = $value;
    }

    /**
     * Mutator for compatibility - set foto (maps to foto_masuk)
     */
    public function setFotoAttribute($value)
    {
        $this->attributes['foto_masuk'] = $value;
    }

    /**
     * Mutator for foto_absensi - maps to foto_masuk or foto_keluar based on context
     * This will be handled in the CreateAbsensi page logic
     */
    public function setFotoAbsensiAttribute($value)
    {
        // This field is handled in CreateAbsensi logic
        // Don't set anything here to avoid conflicts
        // Value is intentionally not used here
        unset($value);
    }

    /**
     * Accessor for compatibility - get latitude (returns latitude_masuk)
     */
    public function getLatitudeAttribute()
    {
        return $this->attributes['latitude_masuk'] ?? null;
    }

    /**
     * Accessor for compatibility - get longitude (returns longitude_masuk)
     */
    public function getLongitudeAttribute()
    {
        return $this->attributes['longitude_masuk'] ?? null;
    }

    /**
     * Accessor for compatibility - get foto (returns foto_masuk)
     */
    public function getFotoAttribute()
    {
        return $this->attributes['foto_masuk'] ?? null;
    }

    /**
     * Get attendance records for a specific employee and date
     */
    public static function getAttendanceForDate($karyawanId, $date)
    {
        return static::with(['jadwal.shift'])
            ->where('karyawan_id', $karyawanId)
            ->whereDate('tanggal_absensi', $date)
            ->orderBy('periode')
            ->get();
    }

    /**
     * Get current attendance status for split shift
     */
    public static function getCurrentAttendanceStatus($karyawanId, $date, $shift)
    {
        $attendances = static::getAttendanceForDate($karyawanId, $date);
        $now = \Carbon\Carbon::now();

        if (!$shift->isSplitShift()) {
            // Regular shift logic
            $attendance = $attendances->where('periode', 1)->first();

            if (!$attendance) {
                return ['action' => 'check_in', 'periode' => 1, 'message' => 'Belum absen masuk'];
            }

            if ($attendance->waktu_masuk && !$attendance->waktu_keluar) {
                return ['action' => 'check_out', 'periode' => 1, 'message' => 'Sudah absen masuk, belum absen keluar'];
            }

            return ['action' => 'completed', 'periode' => 1, 'message' => 'Absensi hari ini sudah lengkap'];
        }

        // Split shift logic
        $currentPeriod = $shift->getCurrentPeriod($now->format('H:i:s'));
        $periode1 = $attendances->where('periode', 1)->first();
        $periode2 = $attendances->where('periode', 2)->first();

        // Check periode 1
        if (!$periode1) {
            if ($currentPeriod == 1) {
                return ['action' => 'check_in', 'periode' => 1, 'message' => 'Absen masuk periode 1'];
            } else {
                return ['action' => 'check_in', 'periode' => 1, 'message' => 'Belum absen periode 1 (terlambat)'];
            }
        }

        if ($periode1->waktu_masuk && !$periode1->waktu_keluar) {
            return ['action' => 'check_out', 'periode' => 1, 'message' => 'Absen keluar periode 1'];
        }

        // Check periode 2
        if (!$periode2) {
            if ($currentPeriod == 2) {
                return ['action' => 'check_in', 'periode' => 2, 'message' => 'Absen masuk periode 2'];
            } else {
                return ['action' => 'waiting', 'periode' => 2, 'message' => 'Menunggu waktu periode 2'];
            }
        }

        if ($periode2->waktu_masuk && !$periode2->waktu_keluar) {
            return ['action' => 'check_out', 'periode' => 2, 'message' => 'Absen keluar periode 2'];
        }

        return ['action' => 'completed', 'periode' => 2, 'message' => 'Absensi split shift hari ini sudah lengkap'];
    }

    /**
     * Get period display name
     */
    public function getPeriodeDisplayAttribute()
    {
        return $this->periode == 1 ? 'Periode 1' : 'Periode 2';
    }

    /**
     * Validate geofencing for attendance
     */
    public static function validateGeofencing($karyawanId, $latitude, $longitude): array
    {
        $karyawan = Karyawan::find($karyawanId);

        if (!$karyawan) {
            return [
                'allowed' => false,
                'message' => 'Karyawan tidak ditemukan',
                'error' => true
            ];
        }

        return \App\Services\GeofencingService::validateAttendanceLocation(
            $karyawan,
            $latitude,
            $longitude
        );
    }

    /**
     * Get location coordinates for attendance
     */
    public function getLocationCoordinatesAttribute(): ?string
    {
        if ($this->latitude_masuk && $this->longitude_masuk) {
            return "{$this->latitude_masuk}, {$this->longitude_masuk}";
        }
        return null;
    }

    /**
     * Get distance from entitas location
     */
    public function getDistanceFromEntitasAttribute(): ?float
    {
        // Get entitas from schedule first, then fallback to karyawan
        $entitas = $this->entitas;

        if (!$entitas) {
            return null;
        }

        if (
            !$entitas->latitude || !$entitas->longitude ||
            !$this->latitude_masuk || !$this->longitude_masuk
        ) {
            return null;
        }

        return \App\Services\GeofencingService::calculateDistance(
            $entitas->latitude,
            $entitas->longitude,
            $this->latitude_masuk,
            $this->longitude_masuk
        );
    }

    /**
     * Check if attendance location is valid
     */
    public function getIsLocationValidAttribute(): bool
    {
        // Get entitas from schedule first, then fallback to karyawan
        $entitas = $this->entitas;

        if (!$entitas) {
            return true; // No validation if no entitas
        }

        if (!$entitas->enable_geofencing) {
            return true; // No validation if geofencing disabled
        }

        $distance = $this->distance_from_entitas;

        if ($distance === null) {
            return false; // Invalid if can't calculate distance
        }

        return $distance <= $entitas->radius;
    }
}
