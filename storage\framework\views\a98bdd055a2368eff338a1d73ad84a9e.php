<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Form untuk pilih karyawan -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <?php echo e($this->form); ?>

        </div>

        <!-- Permission Summary -->
        <!--[if BLOCK]><![endif]--><?php if($selectedKaryawan && !empty($permissionSummary)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <!-- Header Karyawan -->
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <!--[if BLOCK]><![endif]--><?php if($selectedKaryawan->foto_profil): ?>
                                <img src="<?php echo e(Storage::url($selectedKaryawan->foto_profil)); ?>" 
                                     alt="<?php echo e($selectedKaryawan->nama_lengkap); ?>" 
                                     class="w-16 h-16 rounded-full object-cover">
                            <?php else: ?>
                                <div class="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white"><?php echo e($selectedKaryawan->nama_lengkap); ?></h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($selectedKaryawan->nip); ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <?php echo e($selectedKaryawan->jabatan->nama_jabatan ?? 'Jabatan tidak tersedia'); ?>

                                <!--[if BLOCK]><![endif]--><?php if($selectedKaryawan->divisi): ?>
                                    - <?php echo e($selectedKaryawan->divisi->nama_divisi); ?>

                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($selectedKaryawan->departemen): ?>
                                    - <?php echo e($selectedKaryawan->departemen->nama_departemen); ?>

                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </p>
                            <!--[if BLOCK]><![endif]--><?php if($selectedKaryawan->entitas): ?>
                                <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($selectedKaryawan->entitas->nama); ?></p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>

                <!-- Permission Grid -->
                <div class="p-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Permission yang Dimiliki</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $permissionSummary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permissionType => $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 
                                <?php echo e($permission['has_permission'] ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-gray-50 dark:bg-gray-800'); ?>">
                                
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="font-medium text-gray-900 dark:text-white"><?php echo e($permission['label']); ?></h5>
                                    <!--[if BLOCK]><![endif]--><?php if($permission['has_permission']): ?>
                                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>

                                <!--[if BLOCK]><![endif]--><?php if($permission['has_permission']): ?>
                                    <div class="space-y-2">
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $permission['scopes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scope): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="text-sm">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    <?php if($scope['scope_type'] === 'all'): ?> bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                    <?php elseif($scope['scope_type'] === 'entitas'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                    <?php elseif($scope['scope_type'] === 'departemen'): ?> bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                                    <?php elseif($scope['scope_type'] === 'divisi'): ?> bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                                    <?php else: ?> bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                                    <?php endif; ?>">
                                                    <?php echo e($scope['scope_label']); ?>

                                                </span>
                                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1"><?php echo e($scope['scope_description']); ?></p>
                                                <!--[if BLOCK]><![endif]--><?php if($scope['description']): ?>
                                                    <p class="text-xs text-gray-500 dark:text-gray-500 italic"><?php echo e($scope['description']); ?></p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                <?php else: ?>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Tidak memiliki permission ini</p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 rounded-b-lg">
                    <div class="flex justify-end space-x-3">
                        <a href="<?php echo e(route('filament.admin.resources.karyawan-permissions.create', ['karyawan_id' => $selectedKaryawan->id])); ?>" 
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Tambah Permission
                        </a>
                        
                        <a href="<?php echo e(route('filament.admin.resources.karyawan-permissions.index', ['tableFilters[karyawan][value]' => $selectedKaryawan->id])); ?>" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Kelola Permission
                        </a>
                    </div>
                </div>
            </div>
        <?php elseif($selectedKaryawan && empty($permissionSummary)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Belum Ada Permission</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Karyawan ini belum memiliki permission khusus.</p>
                <div class="mt-6">
                    <a href="<?php echo e(route('filament.admin.resources.karyawan-permissions.create', ['karyawan_id' => $selectedKaryawan->id])); ?>" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Permission Pertama
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Pilih Karyawan</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Pilih karyawan dari dropdown di atas untuk melihat ringkasan permission.</p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/permission-summary.blade.php ENDPATH**/ ?>