<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Absensi;
use App\Observers\UserObserver;
use App\Observers\KaryawanObserver;
use App\Observers\CutiIzinObserver;
use App\Observers\AbsensiObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        User::observe(UserObserver::class);
        <PERSON><PERSON><PERSON>::observe(KaryawanObserver::class);
        CutiIzin::observe(CutiIzinObserver::class);
        Absensi::observe(AbsensiObserver::class);

        //untuk ngrok
        if (
            app()->environment('local') &&
            (
                request()->isSecure()
                || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')
            )
        ) {
            \URL::forceScheme('https');
        }
    }
}
