<?php

namespace App\Filament\Pages;

use Filament\Support\Exceptions\Halt;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Traits\HasAdvancedDashboardFilters;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Schedule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SupervisorDashboard extends Dashboard implements HasForms
{
    use InteractsWithForms, HasAdvancedDashboardFilters;

    protected static ?string $title = 'Dashboard Supervisor';

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Dashboard Supervisor';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?int $navigationSort = 1;
    protected static string $routePath = '/supervisor-dashboard';
    protected static string $view = 'filament.pages.advanced-dashboard-with-filters';

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\SupervisorStatsOverview::class,
            \App\Filament\Widgets\ScheduleTable::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }

    public $tanggalMulai;
    public $tanggalSelesai;
    public $shift;
    public $karyawan = [];
    public $keterangan;

    public function mount(): void
    {
        parent::mount();

        $this->tanggalMulai = now()->format('Y-m-d');
        $this->tanggalSelesai = now()->format('Y-m-d');

        // Check if user is supervisor or admin
        // $user = Auth::user();
        // if (!($user->role === 'supervisor' || $user->role === 'admin')) {
        //     Notification::make()
        //         ->title('Akses Ditolak')
        //         ->body('Anda tidak memiliki akses ke halaman ini.')
        //         ->danger()
        //         ->send();

        //     redirect()->to('/admin');
        // }
    }

    protected function getFormSchema(): array
    {
        return [
            DatePicker::make('tanggalMulai')
                ->label('Tanggal Mulai')
                ->required()
                ->default(now()),

            DatePicker::make('tanggalSelesai')
                ->label('Tanggal Selesai')
                ->required()
                ->default(now())
                ->minDate(fn(callable $get) => $get('tanggalMulai')),

            Select::make('shift')
                ->label('Shift')
                ->options(Shift::where('is_active', true)->pluck('nama_shift', 'id'))
                ->required()
                ->searchable(),

            CheckboxList::make('karyawan')
                ->label('Pilih Karyawan')
                ->options(function () {
                    // Only show employees supervised by this user
                    $user = Auth::user();
                    $query = Karyawan::query()
                        ->where('status_aktif', 1)
                        ->orderBy('nama_lengkap');

                    if ($user->role === 'keptok') {
                        $employeeIds = Karyawan::where('id_entitas', $user->id)->pluck('id')->toArray();
                        $query->whereIn('id', $employeeIds);
                    }
                    elseif ($user->role === 'supervisor') {
                        $employeeIds = Karyawan::where('id_divisi', $user->id)->pluck('id')->toArray();
                        $query->whereIn('id', $employeeIds);
                    }

                    elseif ($user->role === 'manager') {
                        $employeeIds = Karyawan::where('id_departemen', $user->id)->pluck('id')->toArray();
                        $query->whereIn('id', $employeeIds);
                    }

                    return $query->pluck('nama_lengkap', 'id');
                })
                ->required()
                ->searchable()
                ->columns(2),

            TextInput::make('keterangan')
                ->label('Keterangan')
                ->maxLength(255),
        ];
    }

    // public function create(): void
    // {
    //     $data = $this->form->getState();

    //     try {
    //         DB::beginTransaction();

    //         $shift = Shift::findOrFail($data['shift']);
    //         $startDate = Carbon::parse($data['tanggalMulai']);
    //         $endDate = Carbon::parse($data['tanggalSelesai']);
    //         $karyawanIds = $data['karyawan'];
    //         $keterangan = $data['keterangan'] ?? '';

    //         // Loop through each day in the date range
    //         $currentDate = clone $startDate;
    //         $createdCount = 0;
    //         $skippedCount = 0;

    //         while ($currentDate <= $endDate) {
    //             // Loop through each employee
    //             foreach ($karyawanIds as $karyawanId) {
    //                 // Check if a schedule already exists for this employee on this date
    //                 $existingSchedule = Schedule::where('karyawan_id', $karyawanId)
    //                     ->where('tanggal_jadwal', $currentDate->format('Y-m-d'))
    //                     ->first();

    //                 // If no schedule exists, create one
    //                 if (!$existingSchedule) {
    //                     Schedule::create([
    //                         'karyawan_id' => $karyawanId,
    //                         'shift_id' => $shift->id,
    //                         'supervisor_id' => Auth::id(),
    //                         'tanggal_jadwal' => $currentDate->format('Y-m-d'),
    //                         'waktu_masuk' => $shift->waktu_mulai,
    //                         'waktu_keluar' => $shift->waktu_selesai,
    //                         'status' => 'Hadir', // Default status
    //                         'keterangan' => $keterangan,
    //                         'is_approved' => true, // Auto-approve schedules created by supervisor
    //                     ]);

    //                     $createdCount++;
    //                 } else {
    //                     $skippedCount++;
    //                 }
    //             }

    //             // Move to the next day
    //             $currentDate->addDay();
    //         }

    //         DB::commit();

    //         Notification::make()
    //             ->title('Jadwal Berhasil Dibuat')
    //             ->body("Berhasil membuat {$createdCount} jadwal baru. {$skippedCount} jadwal dilewati karena sudah ada.")
    //             ->success()
    //             ->send();

    //         // Reset form
    //         $this->form->fill([
    //             'tanggalMulai' => now()->format('Y-m-d'),
    //             'tanggalSelesai' => now()->format('Y-m-d'),
    //             'shift' => null,
    //             'karyawan' => [],
    //             'keterangan' => null,
    //         ]);
    //     } catch (Halt $exception) {
    //         DB::rollBack();

    //         throw $exception;
    //     } catch (\Exception $exception) {
    //         DB::rollBack();

    //         Notification::make()
    //             ->title('Error')
    //             ->body('Terjadi kesalahan: ' . $exception->getMessage())
    //             ->danger()
    //             ->send();
    //     }
    // }

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         Action::make('create')
    //             ->label('Buat Jadwal')
    //             ->action('create')
    //             ->color('primary'),
    //     ];
    // }
}
