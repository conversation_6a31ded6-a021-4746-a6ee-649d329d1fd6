<div x-data="karyawanDivisiSelector()" class="space-y-4">
    @if ($divisiGroups->isEmpty())
        <div class="text-center py-8 text-gray-500">
            <div class="text-4xl mb-2">👥</div>
            @if (empty($entitasId))
                <p>Pilih entitas terlebih dahulu</p>
                <p class="text-sm">Pilih entitas untuk menampilkan karyawan yang dapat dipilih</p>
            @else
                <p>Tidak ada karyawan yang dapat dipilih</p>
                <p class="text-sm">Pastikan Anda memiliki permission untuk mengelola jadwal</p>
            @endif
        </div>
    @else
        <!-- Summary Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <span class="text-blue-600">📊</span>
                    <span class="font-medium text-blue-800">
                        Total: {{ $divisiGroups->flatten()->count() }} karyawan dalam {{ $divisiGroups->count() }}
                        divisi
                    </span>
                </div>
                <div class="text-sm text-blue-600">
                    <span x-text="selectedCount"></span> karyawan dipilih
                </div>
            </div>
        </div>

        <!-- Divisi Groups -->
        @foreach ($divisiGroups as $divisiName => $karyawanList)
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <!-- Divisi Header -->
                <div class="bg-gray-50 border-b border-gray-200 p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">📂</span>
                            <div>
                                <h3 class="font-semibold text-gray-900">{{ $divisiName }}</h3>
                                <p class="text-sm text-gray-600">{{ $karyawanList->count() }} karyawan</p>
                            </div>
                        </div>

                        <!-- Select All for this Divisi -->
                        <div class="flex items-center space-x-2">
                            <button type="button" @click="selectAllInDivisi('{{ $divisiName }}')"
                                class="text-xs bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded-full transition-colors">
                                ✓ Pilih Semua
                            </button>
                            <button type="button" @click="deselectAllInDivisi('{{ $divisiName }}')"
                                class="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-full transition-colors">
                                ✗ Hapus Semua
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Karyawan List -->
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        @foreach ($karyawanList as $karyawan)
                            <label
                                class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="checkbox" value="{{ $karyawan->id }}" x-model="selectedKaryawan"
                                    @change="updateHiddenField()"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    data-divisi="{{ $divisiName }}">
                                <div class="flex-1 min-w-0">
                                    <div class="font-medium text-gray-900 truncate">
                                        {{ $karyawan->nama_lengkap }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ $karyawan->nip }}
                                    </div>
                                </div>
                            </label>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach
    @endif
</div>

<script>
    function karyawanDivisiSelector() {
        return {
            selectedKaryawan: @js($selectedKaryawan ?? []),

            get selectedCount() {
                return this.selectedKaryawan.length;
            },

            selectAllInDivisi(divisiName) {
                const checkboxes = document.querySelectorAll(`input[data-divisi="${divisiName}"]`);
                checkboxes.forEach(checkbox => {
                    const value = parseInt(checkbox.value);
                    if (!this.selectedKaryawan.includes(value)) {
                        this.selectedKaryawan.push(value);
                        checkbox.checked = true;
                    }
                });
                this.updateHiddenField();
            },

            deselectAllInDivisi(divisiName) {
                const checkboxes = document.querySelectorAll(`input[data-divisi="${divisiName}"]`);
                checkboxes.forEach(checkbox => {
                    const value = parseInt(checkbox.value);
                    const index = this.selectedKaryawan.indexOf(value);
                    if (index > -1) {
                        this.selectedKaryawan.splice(index, 1);
                        checkbox.checked = false;
                    }
                });
                this.updateHiddenField();
            },

            updateHiddenField() {
                // Try multiple selectors to find the karyawan field
                const selectors = [
                    'input[name="karyawan"]',
                    'input[wire\\:model="data.karyawan"]',
                    'input[wire\\:model\\.defer="data.karyawan"]',
                    'input[id*="karyawan"]',
                    'input[data-field="karyawan"]'
                ];

                let hiddenField = null;
                for (const selector of selectors) {
                    hiddenField = document.querySelector(selector);
                    if (hiddenField) break;
                }

                console.log('Hidden field found:', hiddenField);
                console.log('Selected karyawan:', this.selectedKaryawan);

                if (hiddenField) {
                    const jsonValue = JSON.stringify(this.selectedKaryawan);
                    hiddenField.value = jsonValue;
                    console.log('Setting hidden field value to:', jsonValue);

                    // Trigger multiple events to ensure Filament/Livewire picks it up
                    const events = ['input', 'change', 'blur', 'keyup'];
                    events.forEach(eventType => {
                        hiddenField.dispatchEvent(new Event(eventType, {
                            bubbles: true
                        }));
                    });

                    // Force Livewire update if available
                    if (window.Livewire && hiddenField.hasAttribute('wire:model')) {
                        const component = hiddenField.closest('[wire\\:id]');
                        if (component) {
                            const componentId = component.getAttribute('wire:id');
                            window.Livewire.find(componentId).set('data.karyawan', jsonValue);
                        }
                    }
                } else {
                    console.error('Hidden field not found! Available inputs:',
                        Array.from(document.querySelectorAll('input')).map(i => i.name || i.id));
                }
            },

            init() {
                // Initialize checkboxes based on selectedKaryawan
                this.$nextTick(() => {
                    this.selectedKaryawan.forEach(id => {
                        const checkbox = document.querySelector(`input[value="${id}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                });
            }
        }
    }
</script>
