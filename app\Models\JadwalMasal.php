<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class JadwalMasal extends Model
{
    use HasFactory;

    protected $table = 'jadwal_masal';

    protected $fillable = [
        'nama_jadwal',
        'tanggal_mulai',
        'tanggal_selesai',
        'shift_id',
        'entitas_id',
        'created_by',
        'keterangan',
        'generated_at',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'generated_at' => 'datetime',
    ];

    /**
     * Get the shift associated with this bulk schedule
     */
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who created this bulk schedule
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the entitas/entity for this bulk schedule
     */
    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'entitas_id');
    }

    /**
     * Get the employees associated with this bulk schedule
     */
    public function karyawan()
    {
        return $this->belongsToMany(Karyawan::class, 'jadwal_masal_karyawan', 'jadwal_masal_id', 'karyawan_id')
            ->withTimestamps();
    }

    /**
     * Generate individual schedules from this bulk schedule
     */
    public function generateSchedules()
    {
        // Check if already generated
        if ($this->generated_at) {
            throw new \Exception('Jadwal masal ini sudah pernah di-generate pada ' . $this->generated_at->format('d M Y H:i'));
        }

        $karyawanIds = $this->karyawan()->pluck('karyawan.id');
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $shift = $this->shift;
        $createdBy = $this->created_by;

        $generatedCount = 0;
        $skippedCount = 0;

        // Loop through each day in the date range
        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            // Loop through each employee
            foreach ($karyawanIds as $karyawanId) {
                // Check if a schedule already exists for this employee on this date
                $existingSchedule = \App\Models\Schedule::where('karyawan_id', $karyawanId)
                    ->where('tanggal_jadwal', $currentDate->format('Y-m-d'))
                    ->first();

                // If no schedule exists, create one
                if (!$existingSchedule) {
                    \App\Models\Schedule::create([
                        'karyawan_id' => $karyawanId,
                        'shift_id' => $shift->id,
                        'supervisor_id' => $createdBy,
                        'tanggal_jadwal' => $currentDate->format('Y-m-d'),
                        'waktu_masuk' => $shift->waktu_mulai,
                        'waktu_keluar' => $shift->waktu_selesai,
                        // Biarkan status dan keterangan kosong - akan diisi manual atau saat absensi
                        'is_approved' => true, // Auto-approve jadwal dari jadwal masal
                    ]);
                    $generatedCount++;
                } else {
                    // $skippedCount++;
                    // timpa aja
                    $existingSchedule->update([
                        'shift_id' => $shift->id,
                        'supervisor_id' => $createdBy,
                        'waktu_masuk' => $shift->waktu_mulai,
                        'waktu_keluar' => $shift->waktu_selesai,
                        'is_approved' => true, // Auto-approve jadwal dari jadwal masal
                    ]);
                    $generatedCount++;
                }
            }

            // Move to the next day
            $currentDate->addDay();
        }

        // Mark as generated
        $this->update(['generated_at' => now()]);

        return [
            'generated' => $generatedCount,
            'skipped' => $skippedCount,
            'total_days' => $startDate->diffInDays($endDate) + 1,
            'total_employees' => $karyawanIds->count()
        ];
    }

    /**
     * Check if this bulk schedule has been generated
     */
    public function isGenerated(): bool
    {
        return !is_null($this->generated_at);
    }

    /**
     * Get generation status text
     */
    public function getGenerationStatusText(): string
    {
        if ($this->isGenerated()) {
            return 'Sudah di-generate pada ' . $this->generated_at->format('d M Y H:i');
        }

        return 'Belum di-generate';
    }

    /**
     * Duplicate this bulk schedule with new date range
     */
    public function duplicate(string $tanggalMulai, string $tanggalSelesai): JadwalMasal
    {
        // Create new jadwal masal with same attributes but new dates
        $duplicatedJadwal = static::create([
            'nama_jadwal' => $this->nama_jadwal . ' (Copy)',
            'tanggal_mulai' => $tanggalMulai,
            'tanggal_selesai' => $tanggalSelesai,
            'shift_id' => $this->shift_id,
            'entitas_id' => $this->entitas_id,
            'created_by' => auth()->id() ?? $this->created_by,
            'keterangan' => $this->keterangan,
            'generated_at' => null, // Reset generation status
        ]);

        // Copy all assigned employees
        $karyawanIds = $this->karyawan()->pluck('karyawan.id');
        if ($karyawanIds->isNotEmpty()) {
            $duplicatedJadwal->karyawan()->attach($karyawanIds);
        }

        return $duplicatedJadwal;
    }
}
