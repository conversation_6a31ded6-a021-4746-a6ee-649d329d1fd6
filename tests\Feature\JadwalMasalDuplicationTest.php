<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\JadwalMasal;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class JadwalMasalDuplicationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $shift;
    protected $entitas;
    protected $karyawan;
    protected $jadwalMasal;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create test shift
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Shift Pagi',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00',
            'is_active' => true
        ]);

        // Create test entitas
        $this->entitas = Entitas::factory()->create([
            'nama' => 'Toko <PERSON>'
        ]);

        // Create test karyawan
        $this->karyawan = Karyawan::factory()->create([
            'id_entitas' => $this->entitas->id,
            'status_aktif' => 1
        ]);

        // Create test jadwal masal
        $this->jadwalMasal = JadwalMasal::create([
            'nama_jadwal' => 'Jadwal Test',
            'tanggal_mulai' => Carbon::today(),
            'tanggal_selesai' => Carbon::today()->addDays(6),
            'shift_id' => $this->shift->id,
            'entitas_id' => $this->entitas->id,
            'created_by' => $this->user->id,
            'keterangan' => 'Test jadwal masal'
        ]);

        // Assign karyawan to jadwal masal
        $this->jadwalMasal->karyawan()->attach($this->karyawan->id);
    }

    public function test_can_duplicate_jadwal_masal()
    {
        $this->actingAs($this->user);

        $newStartDate = Carbon::tomorrow()->format('Y-m-d');
        $newEndDate = Carbon::tomorrow()->addDays(6)->format('Y-m-d');

        // Test duplication
        $duplicatedJadwal = $this->jadwalMasal->duplicate($newStartDate, $newEndDate);

        // Assert new jadwal masal was created
        $this->assertInstanceOf(JadwalMasal::class, $duplicatedJadwal);
        $this->assertNotEquals($this->jadwalMasal->id, $duplicatedJadwal->id);

        // Assert attributes are copied correctly
        $this->assertEquals('Jadwal Test (Copy)', $duplicatedJadwal->nama_jadwal);
        $this->assertEquals($newStartDate, $duplicatedJadwal->tanggal_mulai->format('Y-m-d'));
        $this->assertEquals($newEndDate, $duplicatedJadwal->tanggal_selesai->format('Y-m-d'));
        $this->assertEquals($this->shift->id, $duplicatedJadwal->shift_id);
        $this->assertEquals($this->entitas->id, $duplicatedJadwal->entitas_id);
        $this->assertEquals($this->user->id, $duplicatedJadwal->created_by);
        $this->assertEquals('Test jadwal masal', $duplicatedJadwal->keterangan);
        $this->assertNull($duplicatedJadwal->generated_at);

        // Assert karyawan are copied
        $originalKaryawanIds = $this->jadwalMasal->karyawan()->pluck('karyawan.id')->sort();
        $duplicatedKaryawanIds = $duplicatedJadwal->karyawan()->pluck('karyawan.id')->sort();
        $this->assertEquals($originalKaryawanIds, $duplicatedKaryawanIds);
    }

    public function test_duplicate_resets_generation_status()
    {
        $this->actingAs($this->user);

        // Mark original as generated
        $this->jadwalMasal->update(['generated_at' => now()]);
        $this->assertTrue($this->jadwalMasal->isGenerated());

        // Duplicate
        $duplicatedJadwal = $this->jadwalMasal->duplicate(
            Carbon::tomorrow()->format('Y-m-d'),
            Carbon::tomorrow()->addDays(6)->format('Y-m-d')
        );

        // Assert duplicated jadwal is not marked as generated
        $this->assertFalse($duplicatedJadwal->isGenerated());
        $this->assertNull($duplicatedJadwal->generated_at);
    }

    public function test_duplicate_with_multiple_karyawan()
    {
        $this->actingAs($this->user);

        // Create additional karyawan
        $karyawan2 = Karyawan::factory()->create([
            'id_entitas' => $this->entitas->id,
            'status_aktif' => 1
        ]);
        $karyawan3 = Karyawan::factory()->create([
            'id_entitas' => $this->entitas->id,
            'status_aktif' => 1
        ]);

        // Assign additional karyawan to original jadwal
        $this->jadwalMasal->karyawan()->attach([$karyawan2->id, $karyawan3->id]);

        // Duplicate
        $duplicatedJadwal = $this->jadwalMasal->duplicate(
            Carbon::tomorrow()->format('Y-m-d'),
            Carbon::tomorrow()->addDays(6)->format('Y-m-d')
        );

        // Assert all karyawan are copied
        $this->assertEquals(3, $duplicatedJadwal->karyawan()->count());
        
        $originalKaryawanIds = $this->jadwalMasal->karyawan()->pluck('karyawan.id')->sort();
        $duplicatedKaryawanIds = $duplicatedJadwal->karyawan()->pluck('karyawan.id')->sort();
        $this->assertEquals($originalKaryawanIds, $duplicatedKaryawanIds);
    }
}
